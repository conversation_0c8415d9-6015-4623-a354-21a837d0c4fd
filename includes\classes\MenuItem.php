<?php
/**
 * MenuItem Model Class
 * 
 * Handles menu item operations and management
 */

class MenuItem {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all menu items with category information
     */
    public function getAll($filters = []) {
        $sql = "SELECT mi.*, c.name as category_name 
                FROM menu_items mi 
                LEFT JOIN categories c ON mi.category_id = c.id 
                WHERE 1=1";
        
        $params = [];
        
        if (isset($filters['category_id'])) {
            $sql .= " AND mi.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (isset($filters['is_available'])) {
            $sql .= " AND mi.is_available = :is_available";
            $params['is_available'] = $filters['is_available'];
        }
        
        if (isset($filters['is_featured'])) {
            $sql .= " AND mi.is_featured = :is_featured";
            $params['is_featured'] = $filters['is_featured'];
        }
        
        if (isset($filters['search'])) {
            $sql .= " AND (mi.name LIKE :search OR mi.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $sql .= " ORDER BY c.sort_order, mi.sort_order, mi.name";
        
        $this->db->query($sql);
        
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        
        return $this->db->fetchAll();
    }
    
    /**
     * Get menu items by category
     */
    public function getByCategory($category_id, $available_only = true) {
        $where = ['category_id' => $category_id];
        
        if ($available_only) {
            $where['is_available'] = 1;
        }
        
        return $this->db->select('menu_items', '*', $where, 'sort_order, name');
    }
    
    /**
     * Get single menu item by ID
     */
    public function getById($id) {
        $sql = "SELECT mi.*, c.name as category_name 
                FROM menu_items mi 
                LEFT JOIN categories c ON mi.category_id = c.id 
                WHERE mi.id = :id";
        
        $this->db->query($sql);
        $this->db->bind(':id', $id);
        
        return $this->db->fetch();
    }
    
    /**
     * Create new menu item
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['category_id', 'name', 'price'];
            $errors = validate_required_fields($data, $required);
            
            if (!empty($errors)) {
                return ['success' => false, 'message' => implode(', ', $errors)];
            }
            
            // Validate price
            if (!is_numeric($data['price']) || $data['price'] < 0) {
                return ['success' => false, 'message' => 'Invalid price'];
            }
            
            // Prepare menu item data
            $item_data = [
                'category_id' => $data['category_id'],
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'price' => $data['price'],
                'image_url' => $data['image_url'] ?? '',
                'is_available' => $data['is_available'] ?? 1,
                'is_featured' => $data['is_featured'] ?? 0,
                'preparation_time' => $data['preparation_time'] ?? DEFAULT_PREPARATION_TIME,
                'ingredients' => $data['ingredients'] ?? '',
                'allergens' => $data['allergens'] ?? '',
                'calories' => $data['calories'] ?? null,
                'sort_order' => $data['sort_order'] ?? 0
            ];
            
            $item_id = $this->db->insert('menu_items', $item_data);
            
            log_activity("Menu item created: {$data['name']} (ID: {$item_id})", 'INFO');
            
            return ['success' => true, 'item_id' => $item_id];
            
        } catch (Exception $e) {
            log_activity("Menu item creation error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to create menu item'];
        }
    }
    
    /**
     * Update menu item
     */
    public function update($id, $data) {
        try {
            $update_data = [];
            
            // Only update provided fields
            $allowed_fields = [
                'category_id', 'name', 'description', 'price', 'image_url',
                'is_available', 'is_featured', 'preparation_time', 'ingredients',
                'allergens', 'calories', 'sort_order'
            ];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_data[$field] = $data[$field];
                }
            }
            
            if (empty($update_data)) {
                return ['success' => false, 'message' => 'No data to update'];
            }
            
            // Validate price if provided
            if (isset($update_data['price']) && (!is_numeric($update_data['price']) || $update_data['price'] < 0)) {
                return ['success' => false, 'message' => 'Invalid price'];
            }
            
            $affected_rows = $this->db->update('menu_items', $update_data, ['id' => $id]);
            
            if ($affected_rows > 0) {
                log_activity("Menu item updated: ID {$id}", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'No changes made'];
            }
            
        } catch (Exception $e) {
            log_activity("Menu item update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update menu item'];
        }
    }
    
    /**
     * Delete menu item
     */
    public function delete($id) {
        try {
            // Check if item is used in any orders
            $orders = $this->db->select('order_items', 'id', ['menu_item_id' => $id], '', '1');
            
            if (!empty($orders)) {
                return ['success' => false, 'message' => 'Cannot delete item that has been ordered'];
            }
            
            // Get item info for logging
            $item = $this->getById($id);
            
            $affected_rows = $this->db->delete('menu_items', ['id' => $id]);
            
            if ($affected_rows > 0) {
                // Delete associated image if exists
                if ($item && !empty($item['image_url'])) {
                    delete_file($item['image_url']);
                }
                
                log_activity("Menu item deleted: {$item['name']} (ID: {$id})", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'Menu item not found'];
            }
            
        } catch (Exception $e) {
            log_activity("Menu item deletion error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to delete menu item'];
        }
    }
    
    /**
     * Toggle availability
     */
    public function toggleAvailability($id) {
        try {
            $item = $this->getById($id);
            
            if (!$item) {
                return ['success' => false, 'message' => 'Menu item not found'];
            }
            
            $new_status = $item['is_available'] ? 0 : 1;
            
            $this->db->update('menu_items', 
                ['is_available' => $new_status], 
                ['id' => $id]
            );
            
            $status_text = $new_status ? 'available' : 'unavailable';
            log_activity("Menu item marked as {$status_text}: {$item['name']} (ID: {$id})", 'INFO');
            
            return ['success' => true, 'new_status' => $new_status];
            
        } catch (Exception $e) {
            log_activity("Menu item availability toggle error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update availability'];
        }
    }
    
    /**
     * Get featured items
     */
    public function getFeatured($limit = 6) {
        return $this->db->select('menu_items', '*', 
            ['is_featured' => 1, 'is_available' => 1], 
            'sort_order, name', 
            $limit
        );
    }
    
    /**
     * Search menu items
     */
    public function search($query, $category_id = null) {
        $sql = "SELECT mi.*, c.name as category_name 
                FROM menu_items mi 
                LEFT JOIN categories c ON mi.category_id = c.id 
                WHERE mi.is_available = 1 
                AND (mi.name LIKE :query OR mi.description LIKE :query OR mi.ingredients LIKE :query)";
        
        $params = ['query' => '%' . $query . '%'];
        
        if ($category_id) {
            $sql .= " AND mi.category_id = :category_id";
            $params['category_id'] = $category_id;
        }
        
        $sql .= " ORDER BY mi.name";
        
        $this->db->query($sql);
        
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        
        return $this->db->fetchAll();
    }
    
    /**
     * Get menu statistics
     */
    public function getStatistics() {
        $stats = [];
        
        // Total items
        $stats['total_items'] = $this->db->count('menu_items');
        
        // Available items
        $stats['available_items'] = $this->db->count('menu_items', ['is_available' => 1]);
        
        // Featured items
        $stats['featured_items'] = $this->db->count('menu_items', ['is_featured' => 1]);
        
        // Items by category
        $sql = "SELECT c.name, COUNT(mi.id) as item_count 
                FROM categories c 
                LEFT JOIN menu_items mi ON c.id = mi.category_id 
                GROUP BY c.id, c.name 
                ORDER BY c.sort_order";
        
        $this->db->query($sql);
        $stats['by_category'] = $this->db->fetchAll();
        
        return $stats;
    }
}
?>

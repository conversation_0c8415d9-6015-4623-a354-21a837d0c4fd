<?php
/**
 * Admin Dashboard
 * 
 * Main dashboard with overview statistics and quick actions
 */

// Define application root
define('APP_ROOT', dirname(__DIR__));

// Include configuration
require_once APP_ROOT . '/includes/config.php';

// Require login
require_login();

// Get current user
$current_user = get_logged_in_user();

// Initialize models
$order = new Order();
$menuItem = new MenuItem();
$category = new Category();
$settings = new Settings();

// Get dashboard statistics
$today = date('Y-m-d');
$yesterday = date('Y-m-d', strtotime('-1 day'));
$this_week_start = date('Y-m-d', strtotime('monday this week'));
$this_month_start = date('Y-m-01');

// Order statistics
$today_orders = $order->getStatistics($today, $today);
$yesterday_orders = $order->getStatistics($yesterday, $yesterday);
$week_orders = $order->getStatistics($this_week_start, $today);
$month_orders = $order->getStatistics($this_month_start, $today);

// Recent orders
$recent_orders = $order->getAll(['limit' => 10]);

// Menu statistics
$menu_stats = $menuItem->getStatistics();

// Category statistics
$category_stats = $category->getStatistics();

// Get pending orders count
$pending_orders = $order->getAll(['status' => 'pending']);
$preparing_orders = $order->getAll(['status' => 'preparing']);
$ready_orders = $order->getAll(['status' => 'ready']);

// Calculate percentage changes
$order_change = 0;
$revenue_change = 0;

if ($yesterday_orders['total_orders'] > 0) {
    $order_change = (($today_orders['total_orders'] - $yesterday_orders['total_orders']) / $yesterday_orders['total_orders']) * 100;
}

if ($yesterday_orders['total_revenue'] > 0) {
    $revenue_change = (($today_orders['total_revenue'] - $yesterday_orders['total_revenue']) / $yesterday_orders['total_revenue']) * 100;
}

// Get restaurant name
$restaurant_name = $settings->get('restaurant_name', 'Cafe Jalan Sekolah');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo htmlspecialchars($restaurant_name); ?> Admin</title>
    
    <!-- CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* Admin dashboard styles */
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: var(--gray-100);
        }
        
        .sidebar {
            width: 250px;
            background: var(--dark-color);
            color: var(--white);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-lg);
        }
        
        .sidebar-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-600);
        }
        
        .sidebar-logo {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
        }
        
        .sidebar-title {
            font-size: 1.2rem;
            font-weight: var(--font-weight-bold);
            margin: 0;
        }
        
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-item {
            margin-bottom: var(--spacing-sm);
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            color: var(--white);
            text-decoration: none;
            border-radius: var(--border-radius-md);
            transition: background-color var(--transition-fast);
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: var(--primary-color);
            color: var(--white);
            text-decoration: none;
        }
        
        .nav-icon {
            margin-right: var(--spacing-md);
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: var(--spacing-lg);
            overflow-y: auto;
        }
        
        .header {
            background: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            margin: 0;
            color: var(--dark-color);
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .user-info {
            text-align: right;
        }
        
        .user-name {
            font-weight: var(--font-weight-bold);
            color: var(--dark-color);
        }
        
        .user-role {
            font-size: 0.9rem;
            color: var(--gray-600);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        
        .stat-card {
            background: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: var(--gray-600);
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            color: var(--dark-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .stat-change {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--danger-color);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-lg);
        }
        
        .card {
            background: var(--white);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .card-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }
        
        .card-title {
            margin: 0;
            font-size: 1.1rem;
            color: var(--dark-color);
        }
        
        .card-body {
            padding: var(--spacing-lg);
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .order-info h4 {
            margin: 0 0 var(--spacing-xs);
            font-size: 1rem;
        }
        
        .order-meta {
            font-size: 0.9rem;
            color: var(--gray-600);
        }
        
        .order-status {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: 0.8rem;
            font-weight: var(--font-weight-bold);
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-preparing {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-served {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
            background: var(--primary-color);
            color: var(--white);
            text-decoration: none;
            border-radius: var(--border-radius-md);
            transition: background-color var(--transition-fast);
            font-weight: var(--font-weight-bold);
        }
        
        .action-btn:hover {
            background: var(--primary-dark);
            color: var(--white);
            text-decoration: none;
        }
        
        .action-btn i {
            margin-right: var(--spacing-sm);
        }
        
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                padding: var(--spacing-md);
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-utensils"></i>
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link active">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="orders.php" class="nav-link">
                            <i class="fas fa-shopping-cart nav-icon"></i>
                            Orders
                            <?php if (count($pending_orders) > 0): ?>
                                <span class="badge"><?php echo count($pending_orders); ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="menu.php" class="nav-link">
                            <i class="fas fa-utensils nav-icon"></i>
                            Menu Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <i class="fas fa-tags nav-icon"></i>
                            Categories
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <i class="fas fa-cog nav-icon"></i>
                            Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="logout.php" class="nav-link">
                            <i class="fas fa-sign-out-alt nav-icon"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <h1 class="header-title">Dashboard</h1>
                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($current_user['full_name']); ?></div>
                        <div class="user-role"><?php echo ucfirst($current_user['role']); ?></div>
                    </div>
                    <a href="logout.php" class="btn btn-outline">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </header>
            
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Today's Orders</span>
                        <div class="stat-icon" style="background: var(--primary-color);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo $today_orders['total_orders']; ?></div>
                    <div class="stat-change <?php echo $order_change >= 0 ? 'positive' : 'negative'; ?>">
                        <i class="fas fa-arrow-<?php echo $order_change >= 0 ? 'up' : 'down'; ?>"></i>
                        <?php echo abs(round($order_change, 1)); ?>% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Today's Revenue</span>
                        <div class="stat-icon" style="background: var(--success-color);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo format_currency($today_orders['total_revenue']); ?></div>
                    <div class="stat-change <?php echo $revenue_change >= 0 ? 'positive' : 'negative'; ?>">
                        <i class="fas fa-arrow-<?php echo $revenue_change >= 0 ? 'up' : 'down'; ?>"></i>
                        <?php echo abs(round($revenue_change, 1)); ?>% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Pending Orders</span>
                        <div class="stat-icon" style="background: var(--warning-color);">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo count($pending_orders); ?></div>
                    <div class="stat-change">
                        <i class="fas fa-info-circle"></i>
                        Needs attention
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Menu Items</span>
                        <div class="stat-icon" style="background: var(--accent-color);">
                            <i class="fas fa-utensils"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo $menu_stats['available_items']; ?></div>
                    <div class="stat-change">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $menu_stats['total_items']; ?> total items
                    </div>
                </div>
            </div>
            
            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Recent Orders -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Orders</h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                            <p class="text-muted">No orders yet today.</p>
                        <?php else: ?>
                            <?php foreach (array_slice($recent_orders, 0, 8) as $order): ?>
                                <div class="order-item">
                                    <div class="order-info">
                                        <h4><?php echo htmlspecialchars($order['order_number']); ?></h4>
                                        <div class="order-meta">
                                            Table <?php echo htmlspecialchars($order['table_number']); ?> • 
                                            <?php echo format_currency($order['total_amount']); ?> • 
                                            <?php echo format_date($order['created_at'], 'H:i'); ?>
                                        </div>
                                    </div>
                                    <span class="order-status status-<?php echo $order['status']; ?>">
                                        <?php echo ucfirst($order['status']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="orders.php" class="action-btn">
                                <i class="fas fa-plus"></i>
                                View Orders
                            </a>
                            <a href="menu.php" class="action-btn">
                                <i class="fas fa-utensils"></i>
                                Manage Menu
                            </a>
                            <a href="reports.php" class="action-btn">
                                <i class="fas fa-chart-bar"></i>
                                View Reports
                            </a>
                            <a href="settings.php" class="action-btn">
                                <i class="fas fa-cog"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(function() {
            // Only refresh if user is still on the page
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
        
        // Show notification for new orders
        <?php if (count($pending_orders) > 0): ?>
            RestaurantApp.showAlert('You have <?php echo count($pending_orders); ?> pending order(s)', 'info');
        <?php endif; ?>
    </script>
</body>
</html>

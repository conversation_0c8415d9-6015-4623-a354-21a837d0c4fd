<?php
/**
 * User Model Class
 * 
 * Handles user authentication and management
 */

class User {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Authenticate user login
     */
    public function login($username, $password) {
        try {
            $user = $this->db->select('users', '*', [
                'username' => $username,
                'is_active' => 1
            ]);
            
            if (empty($user)) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            $user = $user[0];
            
            if (!password_verify($password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Update last login
            $this->db->update('users', 
                ['last_login' => date('Y-m-d H:i:s')], 
                ['id' => $user['id']]
            );
            
            // Set session data
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['login_time'] = time();
            
            log_activity("User logged in: {$user['username']}", 'INFO');
            
            return ['success' => true, 'user' => $user];
            
        } catch (Exception $e) {
            log_activity("Login error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        $username = $_SESSION['username'] ?? 'unknown';
        
        // Clear session data
        session_unset();
        session_destroy();
        
        log_activity("User logged out: {$username}", 'INFO');
        
        return ['success' => true];
    }
    
    /**
     * Create new user
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['username', 'email', 'password', 'full_name'];
            $errors = validate_required_fields($data, $required);
            
            if (!empty($errors)) {
                return ['success' => false, 'message' => implode(', ', $errors)];
            }
            
            // Validate email
            if (!validate_email($data['email'])) {
                return ['success' => false, 'message' => 'Invalid email address'];
            }
            
            // Check if username exists
            $existing = $this->db->select('users', 'id', ['username' => $data['username']]);
            if (!empty($existing)) {
                return ['success' => false, 'message' => 'Username already exists'];
            }
            
            // Check if email exists
            $existing = $this->db->select('users', 'id', ['email' => $data['email']]);
            if (!empty($existing)) {
                return ['success' => false, 'message' => 'Email already exists'];
            }
            
            // Hash password
            $password_hash = password_hash($data['password'], PASSWORD_HASH_ALGO);
            
            // Prepare user data
            $user_data = [
                'username' => $data['username'],
                'email' => $data['email'],
                'password_hash' => $password_hash,
                'full_name' => $data['full_name'],
                'role' => $data['role'] ?? 'admin',
                'is_active' => $data['is_active'] ?? 1
            ];
            
            $user_id = $this->db->insert('users', $user_data);
            
            log_activity("New user created: {$data['username']}", 'INFO');
            
            return ['success' => true, 'user_id' => $user_id];
            
        } catch (Exception $e) {
            log_activity("User creation error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to create user'];
        }
    }
    
    /**
     * Update user
     */
    public function update($user_id, $data) {
        try {
            $update_data = [];
            
            // Only update provided fields
            $allowed_fields = ['username', 'email', 'full_name', 'role', 'is_active'];
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_data[$field] = $data[$field];
                }
            }
            
            // Handle password update
            if (!empty($data['password'])) {
                $update_data['password_hash'] = password_hash($data['password'], PASSWORD_HASH_ALGO);
            }
            
            if (empty($update_data)) {
                return ['success' => false, 'message' => 'No data to update'];
            }
            
            $affected_rows = $this->db->update('users', $update_data, ['id' => $user_id]);
            
            if ($affected_rows > 0) {
                log_activity("User updated: ID {$user_id}", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'No changes made'];
            }
            
        } catch (Exception $e) {
            log_activity("User update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update user'];
        }
    }
    
    /**
     * Delete user
     */
    public function delete($user_id) {
        try {
            // Don't allow deletion of current user
            if ($user_id == $_SESSION['user_id']) {
                return ['success' => false, 'message' => 'Cannot delete your own account'];
            }
            
            $affected_rows = $this->db->delete('users', ['id' => $user_id]);
            
            if ($affected_rows > 0) {
                log_activity("User deleted: ID {$user_id}", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'User not found'];
            }
            
        } catch (Exception $e) {
            log_activity("User deletion error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to delete user'];
        }
    }
    
    /**
     * Get user by ID
     */
    public function getById($user_id) {
        $user = $this->db->select('users', '*', ['id' => $user_id]);
        return !empty($user) ? $user[0] : null;
    }
    
    /**
     * Get all users
     */
    public function getAll($filters = []) {
        $where = [];
        
        if (isset($filters['is_active'])) {
            $where['is_active'] = $filters['is_active'];
        }
        
        if (isset($filters['role'])) {
            $where['role'] = $filters['role'];
        }
        
        return $this->db->select('users', 
            'id, username, email, full_name, role, is_active, created_at, last_login', 
            $where, 
            'created_at DESC'
        );
    }
    
    /**
     * Change password
     */
    public function changePassword($user_id, $current_password, $new_password) {
        try {
            $user = $this->getById($user_id);
            
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!password_verify($current_password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Update password
            $password_hash = password_hash($new_password, PASSWORD_HASH_ALGO);
            $this->db->update('users', 
                ['password_hash' => $password_hash], 
                ['id' => $user_id]
            );
            
            log_activity("Password changed for user ID: {$user_id}", 'INFO');
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            log_activity("Password change error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to change password'];
        }
    }
    
    /**
     * Check if user has permission
     */
    public function hasPermission($permission) {
        if (!is_logged_in()) {
            return false;
        }
        
        $user_role = $_SESSION['role'] ?? '';
        
        // Admin has all permissions
        if ($user_role === 'admin') {
            return true;
        }
        
        // Define role permissions
        $permissions = [
            'manager' => ['view_orders', 'manage_menu', 'view_reports']
        ];
        
        return isset($permissions[$user_role]) && 
               in_array($permission, $permissions[$user_role]);
    }
}
?>

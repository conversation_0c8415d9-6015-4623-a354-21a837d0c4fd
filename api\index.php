<?php
/**
 * API Router
 * 
 * Handles AJAX requests and API endpoints
 */

// Define application root
define('APP_ROOT', dirname(__DIR__));

// Include configuration
require_once APP_ROOT . '/includes/config.php';

// Set JSON content type
header('Content-Type: application/json');

// Enable CORS for local development (remove in production)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = $_GET['path'] ?? '';
    
    // Remove leading slash
    $path = ltrim($path, '/');
    
    // Split path into segments
    $segments = array_filter(explode('/', $path));
    
    if (empty($segments)) {
        throw new Exception('Invalid API endpoint');
    }
    
    $endpoint = $segments[0];
    $action = $segments[1] ?? 'index';
    $id = $segments[2] ?? null;
    
    // Route to appropriate handler
    switch ($endpoint) {
        case 'menu':
            handleMenuAPI($action, $id, $method);
            break;
            
        case 'categories':
            handleCategoriesAPI($action, $id, $method);
            break;
            
        case 'orders':
            handleOrdersAPI($action, $id, $method);
            break;
            
        case 'cart':
            handleCartAPI($action, $id, $method);
            break;
            
        case 'auth':
            handleAuthAPI($action, $id, $method);
            break;
            
        case 'settings':
            handleSettingsAPI($action, $id, $method);
            break;
            
        default:
            throw new Exception('Unknown API endpoint: ' . $endpoint);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle menu API requests
 */
function handleMenuAPI($action, $id, $method) {
    $menuItem = new MenuItem();
    
    switch ($action) {
        case 'list':
            $filters = [];
            if (isset($_GET['category_id'])) {
                $filters['category_id'] = $_GET['category_id'];
            }
            if (isset($_GET['search'])) {
                $filters['search'] = $_GET['search'];
            }
            if (isset($_GET['available_only'])) {
                $filters['is_available'] = 1;
            }
            
            $items = $menuItem->getAll($filters);
            echo json_encode(['success' => true, 'data' => $items]);
            break;
            
        case 'get':
            if (!$id) {
                throw new Exception('Menu item ID required');
            }
            
            $item = $menuItem->getById($id);
            if (!$item) {
                throw new Exception('Menu item not found');
            }
            
            echo json_encode(['success' => true, 'data' => $item]);
            break;
            
        case 'search':
            $query = $_GET['q'] ?? '';
            $category_id = $_GET['category_id'] ?? null;
            
            if (empty($query)) {
                throw new Exception('Search query required');
            }
            
            $results = $menuItem->search($query, $category_id);
            echo json_encode(['success' => true, 'data' => $results]);
            break;
            
        case 'featured':
            $limit = $_GET['limit'] ?? 6;
            $items = $menuItem->getFeatured($limit);
            echo json_encode(['success' => true, 'data' => $items]);
            break;
            
        default:
            throw new Exception('Unknown menu action: ' . $action);
    }
}

/**
 * Handle categories API requests
 */
function handleCategoriesAPI($action, $id, $method) {
    $category = new Category();
    
    switch ($action) {
        case 'list':
            $active_only = isset($_GET['active_only']) ? (bool)$_GET['active_only'] : true;
            $with_counts = isset($_GET['with_counts']) ? (bool)$_GET['with_counts'] : false;
            
            if ($with_counts) {
                $categories = $category->getWithItemCounts($active_only);
            } else {
                $categories = $category->getAll($active_only);
            }
            
            echo json_encode(['success' => true, 'data' => $categories]);
            break;
            
        case 'get':
            if (!$id) {
                throw new Exception('Category ID required');
            }
            
            $cat = $category->getById($id);
            if (!$cat) {
                throw new Exception('Category not found');
            }
            
            echo json_encode(['success' => true, 'data' => $cat]);
            break;
            
        default:
            throw new Exception('Unknown category action: ' . $action);
    }
}

/**
 * Handle orders API requests
 */
function handleOrdersAPI($action, $id, $method) {
    $order = new Order();
    
    switch ($action) {
        case 'create':
            if ($method !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                throw new Exception('Invalid JSON data');
            }
            
            $result = $order->create($input['order_data'], $input['order_items']);
            echo json_encode($result);
            break;
            
        case 'get':
            if (!$id) {
                throw new Exception('Order ID required');
            }
            
            $order_data = $order->getById($id);
            if (!$order_data) {
                throw new Exception('Order not found');
            }
            
            echo json_encode(['success' => true, 'data' => $order_data]);
            break;
            
        case 'status':
            if ($method !== 'POST') {
                throw new Exception('POST method required');
            }
            
            if (!$id) {
                throw new Exception('Order ID required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $new_status = $input['status'] ?? '';
            $notes = $input['notes'] ?? '';
            
            $result = $order->updateStatus($id, $new_status, $notes);
            echo json_encode($result);
            break;
            
        default:
            throw new Exception('Unknown order action: ' . $action);
    }
}

/**
 * Handle cart API requests
 */
function handleCartAPI($action, $id, $method) {
    switch ($action) {
        case 'add':
            if ($method !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($_SESSION['cart'])) {
                $_SESSION['cart'] = [];
            }
            
            $item_id = $input['item_id'];
            $quantity = $input['quantity'] ?? 1;
            $special_instructions = $input['special_instructions'] ?? '';
            
            // Add or update cart item
            $found = false;
            foreach ($_SESSION['cart'] as &$cart_item) {
                if ($cart_item['item_id'] == $item_id && 
                    $cart_item['special_instructions'] == $special_instructions) {
                    $cart_item['quantity'] += $quantity;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $_SESSION['cart'][] = [
                    'item_id' => $item_id,
                    'quantity' => $quantity,
                    'special_instructions' => $special_instructions
                ];
            }
            
            echo json_encode(['success' => true, 'cart_count' => count($_SESSION['cart'])]);
            break;
            
        case 'remove':
            if ($method !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $item_id = $input['item_id'];
            
            if (isset($_SESSION['cart'])) {
                $_SESSION['cart'] = array_filter($_SESSION['cart'], function($item) use ($item_id) {
                    return $item['item_id'] != $item_id;
                });
                $_SESSION['cart'] = array_values($_SESSION['cart']); // Re-index
            }
            
            echo json_encode(['success' => true, 'cart_count' => count($_SESSION['cart'] ?? [])]);
            break;
            
        case 'get':
            $cart = $_SESSION['cart'] ?? [];
            echo json_encode(['success' => true, 'data' => $cart]);
            break;
            
        case 'clear':
            $_SESSION['cart'] = [];
            echo json_encode(['success' => true]);
            break;
            
        default:
            throw new Exception('Unknown cart action: ' . $action);
    }
}

/**
 * Handle auth API requests
 */
function handleAuthAPI($action, $id, $method) {
    $user = new User();
    
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                throw new Exception('POST method required');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
            
            $result = $user->login($username, $password);
            echo json_encode($result);
            break;
            
        case 'logout':
            $result = $user->logout();
            echo json_encode($result);
            break;
            
        case 'check':
            $is_logged_in = is_logged_in();
            $user_data = $is_logged_in ? get_current_user() : null;
            
            echo json_encode([
                'success' => true,
                'logged_in' => $is_logged_in,
                'user' => $user_data
            ]);
            break;
            
        default:
            throw new Exception('Unknown auth action: ' . $action);
    }
}

/**
 * Handle settings API requests
 */
function handleSettingsAPI($action, $id, $method) {
    $settings = new Settings();
    
    switch ($action) {
        case 'get':
            if ($id) {
                $value = $settings->get($id);
                echo json_encode(['success' => true, 'data' => $value]);
            } else {
                $all_settings = $settings->getAll();
                echo json_encode(['success' => true, 'data' => $all_settings]);
            }
            break;
            
        case 'restaurant':
            $restaurant_settings = $settings->getRestaurantSettings();
            echo json_encode(['success' => true, 'data' => $restaurant_settings]);
            break;
            
        default:
            throw new Exception('Unknown settings action: ' . $action);
    }
}
?>

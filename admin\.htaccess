# Admin Panel Security
# Protect admin directory with additional security measures

# Deny access to sensitive files
<Files "*.log">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.sql">
    Order allow,deny
    Den<PERSON> from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;"
</IfModule>

# Prevent directory browsing
Options -Indexes

# Custom error pages
ErrorDocument 403 /admin/error.php?code=403
ErrorDocument 404 /admin/error.php?code=404
ErrorDocument 500 /admin/error.php?code=500

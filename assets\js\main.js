/**
 * Restaurant Food Ordering System - Main JavaScript File
 * 
 * Contains common JavaScript functions and utilities used throughout the application
 */

// Global application object
const RestaurantApp = {
    // Configuration
    config: {
        apiUrl: '/api',
        currency: '$',
        taxRate: 0.08
    },
    
    // Initialize the application
    init: function() {
        this.bindEvents();
        this.initComponents();
        console.log('Restaurant App initialized');
    },
    
    // Bind global events
    bindEvents: function() {
        // Handle AJAX form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle modal triggers
        document.addEventListener('click', this.handleModalTriggers.bind(this));
        
        // Handle quantity controls
        document.addEventListener('click', this.handleQuantityControls.bind(this));
        
        // Handle search functionality
        const searchInputs = document.querySelectorAll('[data-search]');
        searchInputs.forEach(input => {
            input.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        });
    },
    
    // Initialize components
    initComponents: function() {
        this.initTooltips();
        this.initAlerts();
        this.initImageLazyLoading();
    },
    
    // Handle form submissions
    handleFormSubmit: function(e) {
        const form = e.target;
        if (form.hasAttribute('data-ajax')) {
            e.preventDefault();
            this.submitFormAjax(form);
        }
    },
    
    // Submit form via AJAX
    submitFormAjax: function(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.href;
        const method = form.method || 'POST';
        
        // Show loading state
        const submitBtn = form.querySelector('[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Loading...';
        submitBtn.disabled = true;
        
        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert(data.message || 'Operation successful', 'success');
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
            } else {
                this.showAlert(data.message || 'An error occurred', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    },
    
    // Handle modal triggers
    handleModalTriggers: function(e) {
        const trigger = e.target.closest('[data-modal]');
        if (trigger) {
            e.preventDefault();
            const modalId = trigger.getAttribute('data-modal');
            this.openModal(modalId);
        }
        
        const closeBtn = e.target.closest('[data-modal-close]');
        if (closeBtn) {
            e.preventDefault();
            this.closeModal();
        }
    },
    
    // Handle quantity controls
    handleQuantityControls: function(e) {
        const btn = e.target.closest('[data-quantity]');
        if (btn) {
            e.preventDefault();
            const action = btn.getAttribute('data-quantity');
            const input = btn.parentElement.querySelector('input[type="number"]');
            
            if (action === 'increase') {
                input.value = parseInt(input.value) + 1;
            } else if (action === 'decrease' && parseInt(input.value) > 1) {
                input.value = parseInt(input.value) - 1;
            }
            
            // Trigger change event
            input.dispatchEvent(new Event('change'));
        }
    },
    
    // Handle search functionality
    handleSearch: function(e) {
        const input = e.target;
        const searchTerm = input.value.toLowerCase();
        const targetSelector = input.getAttribute('data-search');
        const targets = document.querySelectorAll(targetSelector);
        
        targets.forEach(target => {
            const text = target.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                target.style.display = '';
            } else {
                target.style.display = 'none';
            }
        });
    },
    
    // Utility: Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Show alert message
    showAlert: function(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            <span>${message}</span>
            <button type="button" class="alert-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    },
    
    // Create alert container if it doesn't exist
    createAlertContainer: function() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'alert-container';
        document.body.insertBefore(container, document.body.firstChild);
        return container;
    },
    
    // Open modal
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            document.body.classList.add('modal-open');
        }
    },
    
    // Close modal
    closeModal: function() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        document.body.classList.remove('modal-open');
    },
    
    // Initialize tooltips
    initTooltips: function() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    },
    
    // Show tooltip
    showTooltip: function(e) {
        const element = e.target;
        const text = element.getAttribute('data-tooltip');
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    },
    
    // Hide tooltip
    hideTooltip: function(e) {
        const element = e.target;
        if (element._tooltip) {
            element._tooltip.remove();
            delete element._tooltip;
        }
    },
    
    // Initialize alerts
    initAlerts: function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(alert => {
            const closeBtn = alert.querySelector('.alert-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    alert.remove();
                });
            }
        });
    },
    
    // Initialize lazy loading for images
    initImageLazyLoading: function() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    },
    
    // Format currency
    formatCurrency: function(amount) {
        return this.config.currency + parseFloat(amount).toFixed(2);
    },
    
    // Calculate total with tax
    calculateTotal: function(subtotal) {
        const tax = subtotal * this.config.taxRate;
        return {
            subtotal: subtotal,
            tax: tax,
            total: subtotal + tax
        };
    }
};

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    RestaurantApp.init();
});

// Export for use in other scripts
window.RestaurantApp = RestaurantApp;

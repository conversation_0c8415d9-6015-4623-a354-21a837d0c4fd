<?php
/**
 * Base Controller Class
 * 
 * Provides common functionality for all controllers
 */

class BaseController {
    protected $db;
    protected $user;
    protected $data = [];
    
    public function __construct() {
        $this->db = new Database();
        $this->user = get_current_user();
        $this->init();
    }
    
    /**
     * Initialize controller - override in child classes
     */
    protected function init() {
        // Override in child classes
    }
    
    /**
     * Render view with data
     */
    protected function render($view, $data = []) {
        // Merge controller data with passed data
        $data = array_merge($this->data, $data);
        
        // Extract variables for use in view
        extract($data);
        
        // Include view file
        $view_file = APP_ROOT . '/views/' . $view . '.php';
        
        if (file_exists($view_file)) {
            include $view_file;
        } else {
            throw new Exception("View file not found: {$view_file}");
        }
    }
    
    /**
     * Render JSON response
     */
    protected function renderJson($data, $status_code = 200) {
        json_response($data, $status_code);
    }
    
    /**
     * Redirect to URL
     */
    protected function redirect($url, $message = null, $type = 'success') {
        if ($message) {
            redirect_with_message($url, $message, $type);
        } else {
            header('Location: ' . $url);
            exit;
        }
    }
    
    /**
     * Require login
     */
    protected function requireLogin() {
        require_login();
    }
    
    /**
     * Check permission
     */
    protected function requirePermission($permission) {
        if (!$this->user || !$this->hasPermission($permission)) {
            $this->redirect('/admin/login.php', 'Access denied', 'error');
        }
    }
    
    /**
     * Check if user has permission
     */
    protected function hasPermission($permission) {
        $user_model = new User();
        return $user_model->hasPermission($permission);
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCsrf($token) {
        if (!verify_csrf_token($token)) {
            $this->renderJson(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }
    }
    
    /**
     * Get POST data
     */
    protected function getPost($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        
        return isset($_POST[$key]) ? sanitize_input($_POST[$key]) : $default;
    }
    
    /**
     * Get GET data
     */
    protected function getGet($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        
        return isset($_GET[$key]) ? sanitize_input($_GET[$key]) : $default;
    }
    
    /**
     * Get uploaded file
     */
    protected function getFile($key) {
        return isset($_FILES[$key]) ? $_FILES[$key] : null;
    }
    
    /**
     * Validate required fields
     */
    protected function validateRequired($data, $fields) {
        return validate_required_fields($data, $fields);
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($message, $type = 'success') {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    
    /**
     * Get flash message
     */
    protected function getFlash() {
        return get_flash_message();
    }
    
    /**
     * Handle AJAX request
     */
    protected function handleAjax($callback) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && 
            isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            
            try {
                $result = call_user_func($callback);
                $this->renderJson($result);
            } catch (Exception $e) {
                log_activity("AJAX error: " . $e->getMessage(), 'ERROR');
                $this->renderJson(['success' => false, 'message' => 'An error occurred'], 500);
            }
        }
    }
    
    /**
     * Paginate results
     */
    protected function paginate($total_items, $current_page = 1, $items_per_page = ITEMS_PER_PAGE) {
        return get_pagination_data($total_items, $current_page, $items_per_page);
    }
    
    /**
     * Upload file
     */
    protected function uploadFile($file, $target_dir = 'general') {
        return upload_file($file, $target_dir);
    }
    
    /**
     * Delete file
     */
    protected function deleteFile($file_path) {
        return delete_file($file_path);
    }
    
    /**
     * Log activity
     */
    protected function logActivity($message, $level = 'INFO') {
        log_activity($message, $level);
    }
    
    /**
     * Get current timestamp
     */
    protected function now() {
        return date('Y-m-d H:i:s');
    }
    
    /**
     * Format currency
     */
    protected function formatCurrency($amount) {
        return format_currency($amount);
    }
    
    /**
     * Format date
     */
    protected function formatDate($date, $format = 'Y-m-d H:i:s') {
        return format_date($date, $format);
    }
    
    /**
     * Check if request is POST
     */
    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
    
    /**
     * Check if request is GET
     */
    protected function isGet() {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get client IP address
     */
    protected function getClientIp() {
        $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Get user agent
     */
    protected function getUserAgent() {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
    
    /**
     * Set page title
     */
    protected function setTitle($title) {
        $this->data['page_title'] = $title;
    }
    
    /**
     * Add CSS file
     */
    protected function addCss($file) {
        if (!isset($this->data['css_files'])) {
            $this->data['css_files'] = [];
        }
        $this->data['css_files'][] = $file;
    }
    
    /**
     * Add JavaScript file
     */
    protected function addJs($file) {
        if (!isset($this->data['js_files'])) {
            $this->data['js_files'] = [];
        }
        $this->data['js_files'][] = $file;
    }
    
    /**
     * Set meta description
     */
    protected function setMetaDescription($description) {
        $this->data['meta_description'] = $description;
    }
    
    /**
     * Set breadcrumbs
     */
    protected function setBreadcrumbs($breadcrumbs) {
        $this->data['breadcrumbs'] = $breadcrumbs;
    }
}
?>

-- Sample data for Restaurant Food Ordering System
-- This file populates the database with initial test data

USE restaurant_ordering_system;

-- Insert admin users
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin'),
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Restaurant Manager', 'manager');

-- Insert categories
INSERT INTO categories (name, description, sort_order) VALUES
('Coffee', 'Freshly brewed coffee and espresso drinks', 1),
('Desserts', 'Sweet treats and desserts', 2),
('Beverages', 'Cold drinks, juices, and soft drinks', 3),
('Appetizers', 'Starters and small plates', 4),
('Main Courses', 'Full meals and entrees', 5),
('Salads', 'Fresh salads and healthy options', 6);

-- Insert menu items
INSERT INTO menu_items (category_id, name, description, price, is_available, is_featured, preparation_time, calories) VALUES
-- Coffee items
(1, 'Espresso', 'Rich and bold espresso shot', 3.50, TRUE, FALSE, 5, 5),
(1, 'Cappuccino', 'Espresso with steamed milk and foam', 4.50, TRUE, TRUE, 8, 120),
(1, 'Latte', 'Smooth espresso with steamed milk', 5.00, TRUE, TRUE, 8, 150),
(1, 'Americano', 'Espresso with hot water', 3.75, TRUE, FALSE, 5, 10),
(1, 'Mocha', 'Espresso with chocolate and steamed milk', 5.50, TRUE, FALSE, 10, 290),

-- Desserts
(2, 'Chocolate Cake', 'Rich chocolate layer cake', 6.50, TRUE, TRUE, 2, 450),
(2, 'Cheesecake', 'Classic New York style cheesecake', 7.00, TRUE, FALSE, 2, 380),
(2, 'Tiramisu', 'Italian coffee-flavored dessert', 7.50, TRUE, TRUE, 2, 320),
(2, 'Ice Cream Sundae', 'Vanilla ice cream with toppings', 5.50, TRUE, FALSE, 5, 280),

-- Beverages
(3, 'Fresh Orange Juice', 'Freshly squeezed orange juice', 4.00, TRUE, FALSE, 3, 110),
(3, 'Iced Tea', 'Refreshing iced tea', 3.00, TRUE, FALSE, 2, 70),
(3, 'Smoothie Bowl', 'Mixed berry smoothie bowl', 8.50, TRUE, TRUE, 8, 250),
(3, 'Sparkling Water', 'Premium sparkling water', 2.50, TRUE, FALSE, 1, 0),

-- Appetizers
(4, 'Bruschetta', 'Toasted bread with tomato and basil', 8.50, TRUE, FALSE, 10, 180),
(4, 'Chicken Wings', 'Spicy buffalo chicken wings', 12.00, TRUE, TRUE, 15, 420),
(4, 'Mozzarella Sticks', 'Fried mozzarella with marinara sauce', 9.50, TRUE, FALSE, 12, 350),

-- Main Courses
(5, 'Grilled Salmon', 'Fresh salmon with vegetables', 22.00, TRUE, TRUE, 25, 380),
(5, 'Chicken Parmesan', 'Breaded chicken with pasta', 18.50, TRUE, FALSE, 30, 650),
(5, 'Beef Burger', 'Angus beef burger with fries', 15.50, TRUE, TRUE, 20, 720),
(5, 'Vegetarian Pasta', 'Pasta with seasonal vegetables', 14.00, TRUE, FALSE, 18, 480),

-- Salads
(6, 'Caesar Salad', 'Romaine lettuce with Caesar dressing', 11.50, TRUE, FALSE, 8, 220),
(6, 'Greek Salad', 'Fresh vegetables with feta cheese', 12.00, TRUE, TRUE, 8, 180),
(6, 'Quinoa Bowl', 'Healthy quinoa with vegetables', 13.50, TRUE, TRUE, 10, 320);

-- Insert restaurant tables
INSERT INTO restaurant_tables (table_number, capacity, location) VALUES
('T01', 2, 'Window Side'),
('T02', 4, 'Center'),
('T03', 4, 'Center'),
('T04', 6, 'Corner'),
('T05', 2, 'Bar Area'),
('T06', 4, 'Patio'),
('T07', 8, 'Private Room'),
('T08', 4, 'Center'),
('T09', 2, 'Window Side'),
('T10', 6, 'Corner');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('restaurant_name', 'Delicious Bites Restaurant', 'Name of the restaurant'),
('tax_rate', '0.08', 'Tax rate percentage (8%)'),
('currency_symbol', '$', 'Currency symbol'),
('default_preparation_time', '20', 'Default preparation time in minutes'),
('max_table_capacity', '8', 'Maximum table capacity'),
('printer_enabled', '1', 'Enable/disable printer integration'),
('auto_print_orders', '1', 'Automatically print new orders'),
('order_number_prefix', 'ORD', 'Prefix for order numbers'),
('receipt_footer_text', 'Thank you for dining with us!', 'Footer text for receipts'),
('contact_phone', '******-0123', 'Restaurant contact phone'),
('contact_email', '<EMAIL>', 'Restaurant contact email'),
('address', '123 Food Street, Culinary City, CC 12345', 'Restaurant address');

-- Sample orders for testing (optional)
INSERT INTO orders (order_number, table_id, customer_name, status, total_amount, order_type) VALUES
('ORD001', 1, 'John Doe', 'served', 23.50, 'dine_in'),
('ORD002', 3, 'Jane Smith', 'preparing', 45.75, 'dine_in'),
('ORD003', 5, 'Bob Johnson', 'ready', 18.25, 'takeaway');

-- Sample order items
INSERT INTO order_items (order_id, menu_item_id, quantity, unit_price, total_price) VALUES
(1, 3, 2, 5.00, 10.00),
(1, 6, 1, 6.50, 6.50),
(1, 10, 1, 4.00, 4.00),
(2, 16, 1, 22.00, 22.00),
(2, 20, 1, 11.50, 11.50),
(2, 3, 2, 5.00, 10.00),
(3, 2, 1, 4.50, 4.50),
(3, 8, 1, 7.50, 7.50),
(3, 12, 1, 3.00, 3.00);

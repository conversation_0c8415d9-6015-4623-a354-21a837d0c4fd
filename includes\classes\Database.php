<?php
/**
 * Database Class - Database abstraction layer
 * 
 * Provides a simple interface for database operations with prepared statements
 * and error handling.
 */

class Database {
    private $pdo;
    private $stmt;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    /**
     * Prepare a SQL statement
     */
    public function query($sql) {
        $this->stmt = $this->pdo->prepare($sql);
        return $this;
    }
    
    /**
     * Bind parameters to the prepared statement
     */
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        
        $this->stmt->bindValue($param, $value, $type);
        return $this;
    }
    
    /**
     * Execute the prepared statement
     */
    public function execute($params = []) {
        try {
            if (!empty($params)) {
                return $this->stmt->execute($params);
            }
            return $this->stmt->execute();
        } catch (PDOException $e) {
            log_activity("Database error: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }
    
    /**
     * Fetch all results
     */
    public function fetchAll() {
        $this->execute();
        return $this->stmt->fetchAll();
    }
    
    /**
     * Fetch single result
     */
    public function fetch() {
        $this->execute();
        return $this->stmt->fetch();
    }
    
    /**
     * Get row count
     */
    public function rowCount() {
        return $this->stmt->rowCount();
    }
    
    /**
     * Get last insert ID
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * Simple select with WHERE conditions
     */
    public function select($table, $columns = '*', $where = [], $order = '', $limit = '') {
        $sql = "SELECT {$columns} FROM {$table}";
        
        if (!empty($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "{$key} = :{$key}";
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        if (!empty($order)) {
            $sql .= " ORDER BY {$order}";
        }
        
        if (!empty($limit)) {
            $sql .= " LIMIT {$limit}";
        }
        
        $this->query($sql);
        
        if (!empty($where)) {
            foreach ($where as $key => $value) {
                $this->bind(":{$key}", $value);
            }
        }
        
        return $this->fetchAll();
    }
    
    /**
     * Simple insert
     */
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->query($sql);
        
        foreach ($data as $key => $value) {
            $this->bind(":{$key}", $value);
        }
        
        $this->execute();
        return $this->lastInsertId();
    }
    
    /**
     * Simple update
     */
    public function update($table, $data, $where) {
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "{$key} = :{$key}";
        }
        
        $conditions = [];
        foreach ($where as $key => $value) {
            $conditions[] = "{$key} = :where_{$key}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $set) . " WHERE " . implode(' AND ', $conditions);
        
        $this->query($sql);
        
        foreach ($data as $key => $value) {
            $this->bind(":{$key}", $value);
        }
        
        foreach ($where as $key => $value) {
            $this->bind(":where_{$key}", $value);
        }
        
        $this->execute();
        return $this->rowCount();
    }
    
    /**
     * Simple delete
     */
    public function delete($table, $where) {
        $conditions = [];
        foreach ($where as $key => $value) {
            $conditions[] = "{$key} = :{$key}";
        }
        
        $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $conditions);
        
        $this->query($sql);
        
        foreach ($where as $key => $value) {
            $this->bind(":{$key}", $value);
        }
        
        $this->execute();
        return $this->rowCount();
    }
    
    /**
     * Count records
     */
    public function count($table, $where = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table}";
        
        if (!empty($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "{$key} = :{$key}";
            }
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $this->query($sql);
        
        if (!empty($where)) {
            foreach ($where as $key => $value) {
                $this->bind(":{$key}", $value);
            }
        }
        
        $result = $this->fetch();
        return $result['count'];
    }
}
?>

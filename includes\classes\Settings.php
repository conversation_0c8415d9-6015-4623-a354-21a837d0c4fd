<?php
/**
 * Settings Model Class
 * 
 * Handles system settings and configuration
 */

class Settings {
    private $db;
    private static $cache = [];
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get setting value
     */
    public function get($key, $default = null) {
        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }
        
        $setting = $this->db->select('system_settings', 'setting_value', ['setting_key' => $key]);
        
        if (!empty($setting)) {
            $value = $setting[0]['setting_value'];
            self::$cache[$key] = $value;
            return $value;
        }
        
        return $default;
    }
    
    /**
     * Set setting value
     */
    public function set($key, $value, $description = '') {
        try {
            // Check if setting exists
            $existing = $this->db->select('system_settings', 'id', ['setting_key' => $key]);
            
            if (!empty($existing)) {
                // Update existing setting
                $this->db->update('system_settings', 
                    ['setting_value' => $value], 
                    ['setting_key' => $key]
                );
            } else {
                // Create new setting
                $this->db->insert('system_settings', [
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'description' => $description
                ]);
            }
            
            // Update cache
            self::$cache[$key] = $value;
            
            log_activity("Setting updated: {$key} = {$value}", 'INFO');
            
            return ['success' => true];
            
        } catch (Exception $e) {
            log_activity("Setting update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update setting'];
        }
    }
    
    /**
     * Get multiple settings
     */
    public function getMultiple($keys) {
        $settings = [];
        
        foreach ($keys as $key) {
            $settings[$key] = $this->get($key);
        }
        
        return $settings;
    }
    
    /**
     * Set multiple settings
     */
    public function setMultiple($settings) {
        try {
            $this->db->beginTransaction();
            
            foreach ($settings as $key => $value) {
                $this->set($key, $value);
            }
            
            $this->db->commit();
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            log_activity("Multiple settings update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update settings'];
        }
    }
    
    /**
     * Get all settings
     */
    public function getAll() {
        $settings = $this->db->select('system_settings', '*', [], 'setting_key');
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
            self::$cache[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    }
    
    /**
     * Get settings by category/prefix
     */
    public function getByPrefix($prefix) {
        $sql = "SELECT * FROM system_settings WHERE setting_key LIKE :prefix ORDER BY setting_key";
        
        $this->db->query($sql);
        $this->db->bind(':prefix', $prefix . '%');
        
        $settings = $this->db->fetchAll();
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
            self::$cache[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    }
    
    /**
     * Delete setting
     */
    public function delete($key) {
        try {
            $affected_rows = $this->db->delete('system_settings', ['setting_key' => $key]);
            
            if ($affected_rows > 0) {
                // Remove from cache
                unset(self::$cache[$key]);
                
                log_activity("Setting deleted: {$key}", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'Setting not found'];
            }
            
        } catch (Exception $e) {
            log_activity("Setting deletion error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to delete setting'];
        }
    }
    
    /**
     * Clear cache
     */
    public function clearCache() {
        self::$cache = [];
    }
    
    /**
     * Get restaurant settings
     */
    public function getRestaurantSettings() {
        return $this->getByPrefix('restaurant_');
    }
    
    /**
     * Get printer settings
     */
    public function getPrinterSettings() {
        return $this->getByPrefix('printer_');
    }
    
    /**
     * Get order settings
     */
    public function getOrderSettings() {
        return $this->getByPrefix('order_');
    }
    
    /**
     * Get email settings
     */
    public function getEmailSettings() {
        return $this->getByPrefix('email_');
    }
    
    /**
     * Initialize default settings
     */
    public function initializeDefaults() {
        $defaults = [
            'restaurant_name' => 'Cafe Jalan Sekolah',
            'restaurant_phone' => '+60-**********',
            'restaurant_email' => '<EMAIL>',
            'restaurant_address' => 'Jalan Sekolah, Kuala Lumpur, Malaysia',
            'tax_rate' => '0.08',
            'currency_symbol' => '$',
            'default_preparation_time' => '20',
            'max_table_capacity' => '8',
            'printer_enabled' => '1',
            'auto_print_orders' => '1',
            'order_number_prefix' => 'ORD',
            'receipt_footer_text' => 'Thank you for dining with us!',
            'timezone' => 'America/New_York',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'items_per_page' => '20',
            'session_timeout' => '3600',
            'max_upload_size' => '5242880', // 5MB
            'allowed_image_types' => 'jpg,jpeg,png,gif,webp'
        ];
        
        foreach ($defaults as $key => $value) {
            // Only set if doesn't exist
            if ($this->get($key) === null) {
                $this->set($key, $value);
            }
        }
    }
    
    /**
     * Validate setting value
     */
    public function validate($key, $value) {
        $validations = [
            'tax_rate' => function($val) {
                return is_numeric($val) && $val >= 0 && $val <= 1;
            },
            'default_preparation_time' => function($val) {
                return is_numeric($val) && $val > 0;
            },
            'max_table_capacity' => function($val) {
                return is_numeric($val) && $val > 0;
            },
            'session_timeout' => function($val) {
                return is_numeric($val) && $val > 0;
            },
            'max_upload_size' => function($val) {
                return is_numeric($val) && $val > 0;
            },
            'restaurant_email' => function($val) {
                return validate_email($val);
            }
        ];
        
        if (isset($validations[$key])) {
            return $validations[$key]($value);
        }
        
        return true; // No validation rule, assume valid
    }
    
    /**
     * Export settings to array
     */
    public function export() {
        return $this->getAll();
    }
    
    /**
     * Import settings from array
     */
    public function import($settings) {
        try {
            $this->db->beginTransaction();
            
            foreach ($settings as $key => $value) {
                if ($this->validate($key, $value)) {
                    $this->set($key, $value);
                }
            }
            
            $this->db->commit();
            
            log_activity("Settings imported", 'INFO');
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            log_activity("Settings import error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to import settings'];
        }
    }
}

/**
 * Global helper function to get setting
 */
function getSetting($key, $default = null) {
    static $settings_instance = null;
    
    if ($settings_instance === null) {
        $settings_instance = new Settings();
    }
    
    return $settings_instance->get($key, $default);
}

/**
 * Global helper function to set setting
 */
function setSetting($key, $value, $description = '') {
    static $settings_instance = null;
    
    if ($settings_instance === null) {
        $settings_instance = new Settings();
    }
    
    return $settings_instance->set($key, $value, $description);
}
?>

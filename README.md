# Restaurant Food Ordering System

A complete web-based restaurant food ordering system with both admin and customer interfaces, built using modern web technologies.

## Features

### Admin Panel
- **Authentication System**: Secure login/logout with session management
- **Dashboard**: Real-time metrics and order statistics
- **Menu Management**: Full CRUD operations for menu items and categories
- **Order Management**: Real-time order viewing and status management
- **Sales Reporting**: Comprehensive analytics and reporting
- **Printer Integration**: ESC/POS thermal printer support
- **User Management**: Admin and manager role management

### Customer Interface
- **Landing Page**: Table number input and welcome screen
- **Menu Browser**: Categorized menu with search functionality
- **Shopping Cart**: Add/remove items with quantity controls
- **Checkout Process**: Order confirmation and submission
- **Order Tracking**: Order status and history viewing

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, React.js components
- **Backend**: PHP 7.4+ with MVC architecture
- **Database**: MySQL 5.7+ with phpMyAdmin
- **Styling**: Custom CSS with Bootstrap components
- **Security**: CSRF protection, input validation, SQL injection prevention

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- phpMyAdmin (optional, for database management)

### Step 1: Download and Setup
1. Clone or download the project files to your web server directory
2. Ensure your web server has read/write permissions for the project directory

### Step 2: Database Configuration
1. Open `includes/config.php` and update database settings:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'restaurant_ordering_system');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

### Step 3: Database Installation
1. Navigate to `http://your-domain/database/install.php` in your browser
2. The installation script will:
   - Create the database
   - Create all required tables
   - Insert sample data
   - Create default admin user

### Step 4: File Permissions
Ensure the following directories are writable:
```
assets/uploads/
logs/
```

### Step 5: Access the System
- **Customer Interface**: `http://your-domain/index.php`
- **Admin Panel**: `http://your-domain/admin/login.php`

### Default Admin Credentials
- **Username**: admin
- **Password**: password

**Important**: Change the default password immediately after first login!

## Directory Structure

```
/project-root
├── admin/                  # Admin panel files
├── customer/              # Customer interface files
├── assets/               # CSS, JS, images, uploads
│   ├── css/
│   ├── js/
│   └── uploads/
├── includes/             # PHP configuration and classes
│   ├── classes/
│   ├── config.php
│   └── functions.php
├── database/             # SQL schema and installation
│   ├── schema.sql
│   ├── sample_data.sql
│   └── install.php
├── logs/                 # Application logs
└── documentation/        # User guides and documentation
```

## Configuration

### Application Settings
Edit `includes/config.php` to customize:
- Database connection
- File upload settings
- Session configuration
- Security settings
- Printer settings

### System Settings
Use the admin panel to configure:
- Restaurant name and details
- Tax rates
- Order settings
- Printer configuration

## Security Features

- **Password Hashing**: Secure password storage using PHP's password_hash()
- **CSRF Protection**: Cross-site request forgery prevention
- **Input Validation**: Server-side validation and sanitization
- **SQL Injection Prevention**: Prepared statements for all database queries
- **Session Security**: Secure session management with timeouts
- **File Upload Security**: Validated file types and sizes

## API Endpoints

The system includes RESTful API endpoints for:
- Menu items and categories
- Order management
- User authentication
- Real-time updates

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Optimization

- **Image Optimization**: Lazy loading and optimized formats
- **Database Indexing**: Optimized queries with proper indexes
- **Caching**: Session-based caching for frequently accessed data
- **Minification**: Compressed CSS and JavaScript files

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `includes/config.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Issues**
   - Check directory permissions for `assets/uploads/`
   - Verify PHP upload settings (upload_max_filesize, post_max_size)

3. **Session Issues**
   - Ensure PHP sessions are enabled
   - Check session directory permissions

4. **Printer Not Working**
   - Verify printer connection and drivers
   - Check printer settings in admin panel
   - Ensure ESC/POS compatibility

### Error Logs
Check application logs in the `logs/` directory for detailed error information.

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review error logs for specific issues
3. Consult the user documentation in the `documentation/` folder

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Version History

- **v1.0.0** - Initial release with core functionality
  - Admin panel with full menu and order management
  - Customer ordering interface
  - Printer integration
  - Security features and user management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Acknowledgments

- Bootstrap for responsive design components
- Font Awesome for icons
- PHP community for best practices and security guidelines

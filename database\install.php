<?php
/**
 * Database Installation Script
 * 
 * This script creates the database and populates it with initial data
 * Run this file once to set up the database for the restaurant ordering system
 */

// Define application root
define('APP_ROOT', dirname(__DIR__));

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'restaurant_ordering_system';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host={$db_host};charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Restaurant Ordering System - Database Installation</h2>\n";
    echo "<pre>\n";
    
    // Create database if it doesn't exist
    echo "Creating database '{$db_name}'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS {$db_name}");
    echo "✓ Database created successfully\n\n";
    
    // Connect to the new database
    $pdo = new PDO("mysql:host={$db_host};dbname={$db_name};charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute schema file
    echo "Creating database tables...\n";
    $schema_file = __DIR__ . '/schema.sql';
    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: {$schema_file}");
    }
    
    $schema_sql = file_get_contents($schema_file);
    
    // Split SQL statements and execute them
    $statements = array_filter(array_map('trim', explode(';', $schema_sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(--|\/\*|CREATE DATABASE|USE)/', $statement)) {
            try {
                $pdo->exec($statement);
                
                // Extract table name for feedback
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "✓ Created table: {$matches[1]}\n";
                }
            } catch (PDOException $e) {
                echo "✗ Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n";
    
    // Read and execute sample data file
    echo "Inserting sample data...\n";
    $sample_data_file = __DIR__ . '/sample_data.sql';
    if (file_exists($sample_data_file)) {
        $sample_sql = file_get_contents($sample_data_file);
        
        // Split SQL statements and execute them
        $statements = array_filter(array_map('trim', explode(';', $sample_sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|\/\*|USE)/', $statement)) {
                try {
                    $pdo->exec($statement);
                    
                    // Extract table name for feedback
                    if (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                        echo "✓ Inserted data into: {$matches[1]}\n";
                    }
                } catch (PDOException $e) {
                    echo "✗ Error inserting data: " . $e->getMessage() . "\n";
                }
            }
        }
    } else {
        echo "⚠ Sample data file not found, skipping...\n";
    }
    
    echo "\n";
    
    // Verify installation
    echo "Verifying installation...\n";
    
    $tables = [
        'users', 'categories', 'menu_items', 'restaurant_tables', 
        'orders', 'order_items', 'order_status_history', 
        'print_jobs', 'system_settings'
    ];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "✓ Table '{$table}': {$count} records\n";
        } catch (PDOException $e) {
            echo "✗ Error checking table '{$table}': " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    echo "🎉 Database installation completed successfully!\n";
    echo "\nDefault admin credentials:\n";
    echo "Username: admin\n";
    echo "Password: password\n";
    echo "\nYou can now access the admin panel at: /admin/login.php\n";
    echo "Customer interface is available at: /index.php\n";
    
} catch (Exception $e) {
    echo "❌ Installation failed: " . $e->getMessage() . "\n";
    echo "\nPlease check your database configuration and try again.\n";
}

echo "</pre>\n";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Installation - Restaurant Ordering System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h2 {
            color: #e74c3c;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }
        
        pre {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #e74c3c;
            overflow-x: auto;
        }
        
        .success {
            color: #27ae60;
        }
        
        .error {
            color: #e74c3c;
        }
        
        .warning {
            color: #f39c12;
        }
        
        .actions {
            margin-top: 20px;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #c0392b;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="actions">
        <a href="../index.php" class="btn">Go to Customer Interface</a>
        <a href="../admin/login.php" class="btn">Go to Admin Panel</a>
        <a href="install.php" class="btn btn-secondary">Run Installation Again</a>
    </div>
</body>
</html>

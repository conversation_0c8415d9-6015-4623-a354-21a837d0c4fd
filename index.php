<?php
/**
 * Restaurant Food Ordering System - Main Entry Point
 * 
 * This is the main landing page that redirects users to appropriate interfaces
 */

// Define application root
define('APP_ROOT', __DIR__);

// Include configuration
require_once 'includes/config.php';

// Handle table number submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['table_number'])) {
    $table_number = sanitize_input($_POST['table_number']);

    if (empty($table_number)) {
        $error = "Please enter a table number";
    } elseif (!is_numeric($table_number) || $table_number < 1 || $table_number > 999) {
        $error = "Please enter a valid table number (1-999)";
    } else {
        // Store table number in session and redirect to customer interface
        // No need to validate against database - flexible seating arrangement
        $_SESSION['table_number'] = $table_number;
        header('Location: customer/menu.php');
        exit;
    }
}

// Get restaurant settings
$db = new Database();
$settings = $db->select('system_settings');
$restaurant_settings = [];
foreach ($settings as $setting) {
    $restaurant_settings[$setting['setting_key']] = $setting['setting_value'];
}

$restaurant_name = $restaurant_settings['restaurant_name'] ?? 'Cafe Jalan Sekolah';
$contact_phone = $restaurant_settings['contact_phone'] ?? '';
$contact_email = $restaurant_settings['contact_email'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($restaurant_name); ?> - Order Online</title>
    
    <!-- CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* Landing page specific styles */
        .landing-page {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-md);
        }
        
        .welcome-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-xxl);
            text-align: center;
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .welcome-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }
        
        .restaurant-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .restaurant-logo i {
            font-size: 3rem;
            color: var(--white);
        }
        
        .welcome-title {
            color: var(--dark-color);
            font-size: 2.5rem;
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-sm);
        }
        
        .welcome-subtitle {
            color: var(--gray-600);
            font-size: 1.2rem;
            margin-bottom: var(--spacing-xl);
        }
        
        .table-form {
            margin: var(--spacing-xl) 0;
        }
        
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: var(--font-weight-bold);
            color: var(--dark-color);
        }
        
        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius-md);
            font-size: 1.1rem;
            text-align: center;
            transition: border-color var(--transition-fast);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: var(--white);
            padding: var(--spacing-md) var(--spacing-xl);
            font-size: 1.2rem;
            font-weight: var(--font-weight-bold);
            border-radius: var(--border-radius-md);
            cursor: pointer;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-md);
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-xl);
        }
        
        .feature-item {
            background: var(--gray-100);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .feature-text {
            color: var(--dark-color);
            font-size: 0.9rem;
            font-weight: var(--font-weight-bold);
        }
        
        .error-message {
            background: var(--danger-color);
            color: var(--white);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .admin-link {
            position: fixed;
            bottom: var(--spacing-md);
            right: var(--spacing-md);
            background: var(--dark-color);
            color: var(--white);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            text-decoration: none;
            font-size: 0.9rem;
            transition: background-color var(--transition-fast);
        }
        
        .admin-link:hover {
            background: var(--gray-700);
            color: var(--white);
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .welcome-card {
                padding: var(--spacing-xl);
                margin: var(--spacing-md);
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="landing-page">
        <div class="welcome-card">
            <div class="restaurant-logo">
                <i class="fas fa-utensils"></i>
            </div>
            
            <h1 class="welcome-title"><?php echo htmlspecialchars($restaurant_name); ?></h1>
            <p class="welcome-subtitle">Welcome to our cafe! Please enter your table number to start ordering.</p>
            
            <?php if (isset($error)): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="table-form">
                <div class="form-group">
                    <label for="table_number" class="form-label">Table Number</label>
                    <input type="number"
                           id="table_number"
                           name="table_number"
                           class="form-input"
                           placeholder="e.g., 1, 2, 3..."
                           min="1"
                           max="999"
                           value="<?php echo isset($_POST['table_number']) ? htmlspecialchars($_POST['table_number']) : ''; ?>"
                           required
                           autocomplete="off">
                </div>
                
                <button type="submit" class="btn-primary">
                    <i class="fas fa-arrow-right"></i> Start Ordering
                </button>
            </form>
            
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-text">Easy Ordering</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="feature-text">Quick Service</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="feature-text">Quality Food</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Admin access link -->
    <a href="admin/login.php" class="admin-link">
        <i class="fas fa-cog"></i> Admin
    </a>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // Auto-focus on table input
        document.getElementById('table_number').focus();
        
        // Validate table number input (numeric only)
        document.getElementById('table_number').addEventListener('input', function(e) {
            let value = e.target.value;
            // Ensure only numeric values
            if (value && (isNaN(value) || value < 1 || value > 999)) {
                e.target.setCustomValidity('Please enter a number between 1 and 999');
            } else {
                e.target.setCustomValidity('');
            }
        });
    </script>
</body>
</html>

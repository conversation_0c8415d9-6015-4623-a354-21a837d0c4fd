<?php
/**
 * Restaurant Food Ordering System - Configuration File
 * 
 * This file contains all the configuration settings for the application
 * including database connection, security settings, and application constants.
 */

// Prevent direct access
if (!defined('APP_ROOT')) {
    define('APP_ROOT', dirname(__DIR__));
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'cjs');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Restaurant Ordering System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/cjs');
define('ADMIN_URL', APP_URL . '/admin');
define('CUSTOMER_URL', APP_URL . '/customer');

// Security Configuration
define('SESSION_NAME', 'restaurant_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // 1 hour

// File Upload Configuration
define('UPLOAD_DIR', APP_ROOT . '/assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination Configuration
define('ITEMS_PER_PAGE', 20);
define('ADMIN_ITEMS_PER_PAGE', 25);

// Order Configuration
define('DEFAULT_PREPARATION_TIME', 20); // minutes
define('ORDER_NUMBER_PREFIX', 'ORD');
define('TAX_RATE', 0.08); // 8%

// Printer Configuration
define('PRINTER_ENABLED', true);
define('AUTO_PRINT_ORDERS', true);
define('RECEIPT_WIDTH', 48); // characters

// Email Configuration (for notifications)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Restaurant System');

// Timezone
date_default_timezone_set('America/New_York');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Auto-load classes
spl_autoload_register(function ($class) {
    $file = APP_ROOT . '/includes/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Include utility functions
require_once APP_ROOT . '/includes/functions.php';

// Initialize database connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}

// Set global database connection
$GLOBALS['pdo'] = $pdo;
?>

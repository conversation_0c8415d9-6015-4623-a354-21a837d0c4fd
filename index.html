
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getSetting('cafe_name', 'Cafe Jalan Sekolah'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D4A574;
            --accent-color: #F4E4BC;
            --text-dark: #2C1810;
            --text-light: #6B4E3D;
        }
        
        body {
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .welcome-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .welcome-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 100%;
            border: 3px solid var(--secondary-color);
        }
        
        .cafe-logo {
            width: 120px;
            height: 120px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.3);
        }
        
        .cafe-logo i {
            font-size: 3rem;
            color: white;
        }
        
        .welcome-title {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .welcome-subtitle {
            color: var(--text-light);
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .table-input-group {
            margin: 2rem 0;
        }
        
        .table-input {
            border: 2px solid var(--secondary-color);
            border-radius: 15px;
            padding: 1rem;
            font-size: 1.2rem;
            text-align: center;
            background: var(--accent-color);
            transition: all 0.3s ease;
        }
        
        .table-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(139, 69, 19, 0.25);
            background: white;
        }
        
        .start-btn {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 1rem 3rem;
            font-size: 1.3rem;
            font-weight: bold;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
        }
        
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
            background: linear-gradient(45deg, #6B2F0E, var(--primary-color));
        }
        
        .cafe-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature-item {
            background: var(--accent-color);
            padding: 1rem;
            border-radius: 15px;
            text-align: center;
            border: 2px solid var(--secondary-color);
        }
        
        .feature-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .feature-text {
            color: var(--text-dark);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .error-alert {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .welcome-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .cafe-features {
                grid-template-columns: 1fr;
            }
        }
        
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .coffee-bean {
            position: absolute;
            color: rgba(139, 69, 19, 0.1);
            font-size: 2rem;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <!-- Floating decorative elements -->
    <div class="floating-elements">
        <i class="fas fa-coffee coffee-bean" style="top: 10%; left: 10%; animation-delay: 0s;"></i>
        <i class="fas fa-coffee coffee-bean" style="top: 20%; right: 10%; animation-delay: 2s;"></i>
        <i class="fas fa-coffee coffee-bean" style="bottom: 20%; left: 15%; animation-delay: 4s;"></i>
        <i class="fas fa-coffee coffee-bean" style="bottom: 10%; right: 20%; animation-delay: 1s;"></i>
        <i class="fas fa-coffee coffee-bean" style="top: 50%; left: 5%; animation-delay: 3s;"></i>
        <i class="fas fa-coffee coffee-bean" style="top: 60%; right: 5%; animation-delay: 5s;"></i>
    </div>

    <div class="welcome-container">
        <div class="welcome-card">
            <div class="cafe-logo">
                <i class="fas fa-coffee"></i>
            </div>
            
            <h1 class="welcome-title">Welcome to Cafe Jalan Sekolah</h1>
            <h2 class="welcome-subtitle"><?php echo getSetting('cafe_name', 'Cafe Jalan Sekolah'); ?></h2>
            
            <?php if (isset($error)): ?>
                <div class="error-alert">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="table-input-group">
                    <label for="table_number" class="form-label h5 text-muted">Enter Your Table Number</label>
                    <input type="text" 
                           class="form-control table-input" 
                           id="table_number" 
                           name="table_number" 
                           placeholder="e.g., Table 5 or T5"
                           required
                           autocomplete="off">
                </div>
                
                <button type="submit" class="btn start-btn">
                    <i class="fas fa-utensils me-2"></i>
                    Start Ordering
                </button>
            </form>
            
            <div class="cafe-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-text">Easy Ordering</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="feature-text">Fast Service</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="feature-text">Fresh Quality</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on table input
        document.getElementById('table_number').focus();
        
        // Enter key support
        document.getElementById('table_number').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
        
        // Input validation and formatting
        document.getElementById('table_number').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            // Remove any non-alphanumeric characters except spaces
            value = value.replace(/[^A-Z0-9\s]/g, '');
            e.target.value = value;
        });
    </script>
</body>
</html>
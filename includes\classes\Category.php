<?php
/**
 * Category Model Class
 * 
 * Handles menu category operations and management
 */

class Category {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all categories
     */
    public function getAll($active_only = false) {
        $where = [];
        
        if ($active_only) {
            $where['is_active'] = 1;
        }
        
        return $this->db->select('categories', '*', $where, 'sort_order, name');
    }
    
    /**
     * Get category by ID
     */
    public function getById($id) {
        $category = $this->db->select('categories', '*', ['id' => $id]);
        return !empty($category) ? $category[0] : null;
    }
    
    /**
     * Get categories with item counts
     */
    public function getWithItemCounts($active_only = false) {
        $sql = "SELECT c.*, COUNT(mi.id) as item_count 
                FROM categories c 
                LEFT JOIN menu_items mi ON c.id = mi.category_id";
        
        if ($active_only) {
            $sql .= " AND mi.is_available = 1";
        }
        
        $sql .= " WHERE c.is_active = 1 
                  GROUP BY c.id 
                  ORDER BY c.sort_order, c.name";
        
        $this->db->query($sql);
        return $this->db->fetchAll();
    }
    
    /**
     * Create new category
     */
    public function create($data) {
        try {
            // Validate required fields
            $required = ['name'];
            $errors = validate_required_fields($data, $required);
            
            if (!empty($errors)) {
                return ['success' => false, 'message' => implode(', ', $errors)];
            }
            
            // Check if category name exists
            $existing = $this->db->select('categories', 'id', ['name' => $data['name']]);
            if (!empty($existing)) {
                return ['success' => false, 'message' => 'Category name already exists'];
            }
            
            // Prepare category data
            $category_data = [
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'image_url' => $data['image_url'] ?? '',
                'sort_order' => $data['sort_order'] ?? 0,
                'is_active' => $data['is_active'] ?? 1
            ];
            
            $category_id = $this->db->insert('categories', $category_data);
            
            log_activity("Category created: {$data['name']} (ID: {$category_id})", 'INFO');
            
            return ['success' => true, 'category_id' => $category_id];
            
        } catch (Exception $e) {
            log_activity("Category creation error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to create category'];
        }
    }
    
    /**
     * Update category
     */
    public function update($id, $data) {
        try {
            $update_data = [];
            
            // Only update provided fields
            $allowed_fields = ['name', 'description', 'image_url', 'sort_order', 'is_active'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_data[$field] = $data[$field];
                }
            }
            
            if (empty($update_data)) {
                return ['success' => false, 'message' => 'No data to update'];
            }
            
            // Check if new name conflicts with existing category
            if (isset($update_data['name'])) {
                $existing = $this->db->select('categories', 'id', [
                    'name' => $update_data['name']
                ]);
                
                if (!empty($existing) && $existing[0]['id'] != $id) {
                    return ['success' => false, 'message' => 'Category name already exists'];
                }
            }
            
            $affected_rows = $this->db->update('categories', $update_data, ['id' => $id]);
            
            if ($affected_rows > 0) {
                log_activity("Category updated: ID {$id}", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'No changes made'];
            }
            
        } catch (Exception $e) {
            log_activity("Category update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update category'];
        }
    }
    
    /**
     * Delete category
     */
    public function delete($id) {
        try {
            // Check if category has menu items
            $items = $this->db->select('menu_items', 'id', ['category_id' => $id], '', '1');
            
            if (!empty($items)) {
                return ['success' => false, 'message' => 'Cannot delete category that contains menu items'];
            }
            
            // Get category info for logging
            $category = $this->getById($id);
            
            $affected_rows = $this->db->delete('categories', ['id' => $id]);
            
            if ($affected_rows > 0) {
                // Delete associated image if exists
                if ($category && !empty($category['image_url'])) {
                    delete_file($category['image_url']);
                }
                
                log_activity("Category deleted: {$category['name']} (ID: {$id})", 'INFO');
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => 'Category not found'];
            }
            
        } catch (Exception $e) {
            log_activity("Category deletion error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to delete category'];
        }
    }
    
    /**
     * Toggle category status
     */
    public function toggleStatus($id) {
        try {
            $category = $this->getById($id);
            
            if (!$category) {
                return ['success' => false, 'message' => 'Category not found'];
            }
            
            $new_status = $category['is_active'] ? 0 : 1;
            
            $this->db->update('categories', 
                ['is_active' => $new_status], 
                ['id' => $id]
            );
            
            $status_text = $new_status ? 'active' : 'inactive';
            log_activity("Category marked as {$status_text}: {$category['name']} (ID: {$id})", 'INFO');
            
            return ['success' => true, 'new_status' => $new_status];
            
        } catch (Exception $e) {
            log_activity("Category status toggle error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update category status'];
        }
    }
    
    /**
     * Reorder categories
     */
    public function reorder($category_orders) {
        try {
            $this->db->beginTransaction();
            
            foreach ($category_orders as $id => $sort_order) {
                $this->db->update('categories', 
                    ['sort_order' => $sort_order], 
                    ['id' => $id]
                );
            }
            
            $this->db->commit();
            
            log_activity("Categories reordered", 'INFO');
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->db->rollback();
            log_activity("Category reorder error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to reorder categories'];
        }
    }
    
    /**
     * Get category statistics
     */
    public function getStatistics() {
        $stats = [];
        
        // Total categories
        $stats['total_categories'] = $this->db->count('categories');
        
        // Active categories
        $stats['active_categories'] = $this->db->count('categories', ['is_active' => 1]);
        
        // Categories with items
        $sql = "SELECT COUNT(DISTINCT c.id) as count 
                FROM categories c 
                INNER JOIN menu_items mi ON c.id = mi.category_id 
                WHERE c.is_active = 1";
        
        $this->db->query($sql);
        $result = $this->db->fetch();
        $stats['categories_with_items'] = $result['count'];
        
        return $stats;
    }
}
?>

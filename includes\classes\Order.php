<?php
/**
 * Order Model Class
 * 
 * Handles order operations and management
 */

class Order {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Create new order
     */
    public function create($order_data, $order_items) {
        try {
            $this->db->beginTransaction();
            
            // Generate unique order number
            $order_number = $this->generateOrderNumber();
            
            // Calculate totals
            $totals = $this->calculateOrderTotals($order_items);
            
            // Prepare order data
            $order_insert_data = [
                'order_number' => $order_number,
                'table_id' => $order_data['table_id'] ?? null,
                'table_number' => $order_data['table_number'] ?? null, // Store table number directly
                'customer_name' => $order_data['customer_name'] ?? '',
                'customer_phone' => $order_data['customer_phone'] ?? '',
                'status' => 'pending',
                'total_amount' => $totals['total'],
                'tax_amount' => $totals['tax'],
                'special_instructions' => $order_data['special_instructions'] ?? '',
                'order_type' => $order_data['order_type'] ?? 'dine_in',
                'estimated_time' => $this->calculateEstimatedTime($order_items)
            ];
            
            // Insert order
            $order_id = $this->db->insert('orders', $order_insert_data);
            
            // Insert order items
            foreach ($order_items as $item) {
                $item_data = [
                    'order_id' => $order_id,
                    'menu_item_id' => $item['menu_item_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['quantity'] * $item['unit_price'],
                    'special_instructions' => $item['special_instructions'] ?? ''
                ];
                
                $this->db->insert('order_items', $item_data);
            }
            
            // Add status history
            $this->addStatusHistory($order_id, 'pending', 'Order created');
            
            $this->db->commit();
            
            log_activity("Order created: {$order_number} (ID: {$order_id})", 'INFO');
            
            return [
                'success' => true, 
                'order_id' => $order_id, 
                'order_number' => $order_number,
                'total_amount' => $totals['total']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            log_activity("Order creation error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to create order'];
        }
    }
    
    /**
     * Get order by ID with items
     */
    public function getById($order_id) {
        // Get order details
        $sql = "SELECT o.*,
                COALESCE(o.table_number, rt.table_number) as display_table_number,
                rt.capacity
                FROM orders o
                LEFT JOIN restaurant_tables rt ON o.table_id = rt.id
                WHERE o.id = :order_id";
        
        $this->db->query($sql);
        $this->db->bind(':order_id', $order_id);
        $order = $this->db->fetch();
        
        if (!$order) {
            return null;
        }
        
        // Get order items
        $sql = "SELECT oi.*, mi.name, mi.description, mi.image_url 
                FROM order_items oi 
                LEFT JOIN menu_items mi ON oi.menu_item_id = mi.id 
                WHERE oi.order_id = :order_id 
                ORDER BY oi.id";
        
        $this->db->query($sql);
        $this->db->bind(':order_id', $order_id);
        $order['items'] = $this->db->fetchAll();
        
        return $order;
    }
    
    /**
     * Get order by order number
     */
    public function getByOrderNumber($order_number) {
        $sql = "SELECT o.*,
                COALESCE(o.table_number, rt.table_number) as display_table_number
                FROM orders o
                LEFT JOIN restaurant_tables rt ON o.table_id = rt.id
                WHERE o.order_number = :order_number";
        
        $this->db->query($sql);
        $this->db->bind(':order_number', $order_number);
        return $this->db->fetch();
    }
    
    /**
     * Get all orders with filters
     */
    public function getAll($filters = []) {
        $sql = "SELECT o.*,
                COALESCE(o.table_number, rt.table_number) as display_table_number
                FROM orders o
                LEFT JOIN restaurant_tables rt ON o.table_id = rt.id
                WHERE 1=1";
        
        $params = [];
        
        if (isset($filters['status'])) {
            $sql .= " AND o.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (isset($filters['table_id'])) {
            $sql .= " AND o.table_id = :table_id";
            $params['table_id'] = $filters['table_id'];
        }
        
        if (isset($filters['date_from'])) {
            $sql .= " AND DATE(o.created_at) >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (isset($filters['date_to'])) {
            $sql .= " AND DATE(o.created_at) <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (isset($filters['order_type'])) {
            $sql .= " AND o.order_type = :order_type";
            $params['order_type'] = $filters['order_type'];
        }
        
        $sql .= " ORDER BY o.created_at DESC";
        
        if (isset($filters['limit'])) {
            $sql .= " LIMIT " . intval($filters['limit']);
        }
        
        $this->db->query($sql);
        
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        
        return $this->db->fetchAll();
    }
    
    /**
     * Update order status
     */
    public function updateStatus($order_id, $new_status, $notes = '') {
        try {
            $order = $this->getById($order_id);
            
            if (!$order) {
                return ['success' => false, 'message' => 'Order not found'];
            }
            
            $update_data = ['status' => $new_status];
            
            // Set served timestamp if status is served
            if ($new_status === 'served') {
                $update_data['served_at'] = date('Y-m-d H:i:s');
            }
            
            $this->db->update('orders', $update_data, ['id' => $order_id]);
            
            // Add status history
            $this->addStatusHistory($order_id, $new_status, $notes);
            
            log_activity("Order status updated: {$order['order_number']} -> {$new_status}", 'INFO');
            
            return ['success' => true];
            
        } catch (Exception $e) {
            log_activity("Order status update error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to update order status'];
        }
    }
    
    /**
     * Cancel order
     */
    public function cancel($order_id, $reason = '') {
        try {
            $order = $this->getById($order_id);
            
            if (!$order) {
                return ['success' => false, 'message' => 'Order not found'];
            }
            
            if (in_array($order['status'], ['served', 'cancelled'])) {
                return ['success' => false, 'message' => 'Cannot cancel this order'];
            }
            
            $this->db->update('orders', 
                ['status' => 'cancelled'], 
                ['id' => $order_id]
            );
            
            // Add status history
            $this->addStatusHistory($order_id, 'cancelled', $reason);
            
            log_activity("Order cancelled: {$order['order_number']}", 'INFO');
            
            return ['success' => true];
            
        } catch (Exception $e) {
            log_activity("Order cancellation error: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => 'Failed to cancel order'];
        }
    }
    
    /**
     * Get order statistics
     */
    public function getStatistics($date_from = null, $date_to = null) {
        $stats = [];
        
        $where_clause = "WHERE 1=1";
        $params = [];
        
        if ($date_from) {
            $where_clause .= " AND DATE(created_at) >= :date_from";
            $params['date_from'] = $date_from;
        }
        
        if ($date_to) {
            $where_clause .= " AND DATE(created_at) <= :date_to";
            $params['date_to'] = $date_to;
        }
        
        // Total orders
        $sql = "SELECT COUNT(*) as count FROM orders {$where_clause}";
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        $result = $this->db->fetch();
        $stats['total_orders'] = $result['count'];
        
        // Orders by status
        $sql = "SELECT status, COUNT(*) as count FROM orders {$where_clause} GROUP BY status";
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        $stats['by_status'] = $this->db->fetchAll();
        
        // Total revenue
        $sql = "SELECT SUM(total_amount) as revenue FROM orders {$where_clause} AND status != 'cancelled'";
        $this->db->query($sql);
        foreach ($params as $key => $value) {
            $this->db->bind(":{$key}", $value);
        }
        $result = $this->db->fetch();
        $stats['total_revenue'] = $result['revenue'] ?? 0;
        
        // Average order value
        if ($stats['total_orders'] > 0) {
            $stats['average_order_value'] = $stats['total_revenue'] / $stats['total_orders'];
        } else {
            $stats['average_order_value'] = 0;
        }
        
        return $stats;
    }
    
    /**
     * Generate unique order number
     */
    private function generateOrderNumber() {
        do {
            $order_number = ORDER_NUMBER_PREFIX . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $existing = $this->db->select('orders', 'id', ['order_number' => $order_number]);
        } while (!empty($existing));
        
        return $order_number;
    }
    
    /**
     * Calculate order totals
     */
    private function calculateOrderTotals($order_items) {
        $subtotal = 0;
        
        foreach ($order_items as $item) {
            $subtotal += $item['quantity'] * $item['unit_price'];
        }
        
        $tax = $subtotal * TAX_RATE;
        $total = $subtotal + $tax;
        
        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'total' => $total
        ];
    }
    
    /**
     * Calculate estimated preparation time
     */
    private function calculateEstimatedTime($order_items) {
        $max_time = DEFAULT_PREPARATION_TIME;
        
        foreach ($order_items as $item) {
            // Get menu item preparation time
            $menu_item = $this->db->select('menu_items', 'preparation_time', ['id' => $item['menu_item_id']]);
            if (!empty($menu_item)) {
                $item_time = $menu_item[0]['preparation_time'] * $item['quantity'];
                $max_time = max($max_time, $item_time);
            }
        }
        
        return $max_time;
    }
    
    /**
     * Add status history entry
     */
    private function addStatusHistory($order_id, $status, $notes = '') {
        $history_data = [
            'order_id' => $order_id,
            'status' => $status,
            'changed_by' => $_SESSION['username'] ?? 'system',
            'notes' => $notes
        ];
        
        $this->db->insert('order_status_history', $history_data);
    }
}
?>
